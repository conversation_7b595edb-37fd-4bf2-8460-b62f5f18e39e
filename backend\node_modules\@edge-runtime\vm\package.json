{"name": "@edge-runtime/vm", "description": "Low level bindings for creating Web Standard contexts.", "homepage": "https://edge-runtime.vercel.app/packages/vm", "version": "3.2.0", "main": "dist/index.js", "repository": {"directory": "packages/vm", "type": "git", "url": "git+https://github.com/vercel/edge-runtime.git"}, "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "keywords": ["context", "edge", "edge-runtime", "functions", "runtime", "standard", "vm", "web"], "engines": {"node": ">=16"}, "files": ["dist"], "license": "MPL-2.0", "publishConfig": {"access": "public"}, "types": "dist/index.d.ts", "dependencies": {"@edge-runtime/primitives": "4.1.0"}, "devDependencies": {"@types/test-listen": "1.1.2", "@types/ws": "8.5.10", "test-listen": "1.1.0", "ws": "8.16.0"}, "scripts": {"build": "tsc --project ./tsconfig.prod.json", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:build": "rm -rf dist", "clean:node": "rm -rf node_modules", "test": "jest"}}